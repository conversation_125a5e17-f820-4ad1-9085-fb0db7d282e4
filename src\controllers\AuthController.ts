import { Request, Response } from 'express';
import { AuthService } from '@/services/AuthService';
import { ResponseUtil } from '@/utils/response';
import { Logger } from '@/utils/logger';
import { CreateUserRequest, LoginRequest } from '@/types';

export class AuthController {
  static async register(req: Request, res: Response): Promise<void> {
    try {
      const userData: CreateUserRequest = req.body;
      
      const result = await AuthService.registerUser(userData);
      
      // Remove sensitive data from response
      const { password_hash, ...userWithoutPassword } = result.user as any;
      
      ResponseUtil.created(res, 'User registered successfully', {
        user: userWithoutPassword,
        token: result.token,
      });
    } catch (error: any) {
      Logger.error('Registration error', {
        error: error.message,
        email: req.body.email,
      });
      
      ResponseUtil.badRequest(res, error.message);
    }
  }

  static async login(req: Request, res: Response): Promise<void> {
    try {
      const loginData: LoginRequest = req.body;
      
      const result = await AuthService.loginUser(loginData);
      
      // Remove sensitive data from response
      const { password_hash, ...userWithoutPassword } = result.user as any;
      
      ResponseUtil.success(res, 'Login successful', {
        user: userWithoutPassword,
        token: result.token,
      });
    } catch (error: any) {
      Logger.error('Login error', {
        error: error.message,
        email: req.body.email,
      });
      
      ResponseUtil.badRequest(res, error.message);
    }
  }

  static async getProfile(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      
      const user = await AuthService.getUserById(userId);
      if (!user) {
        ResponseUtil.notFound(res, 'User not found');
        return;
      }
      
      // Remove sensitive data from response
      const { password_hash, ...userWithoutPassword } = user as any;
      
      ResponseUtil.success(res, 'Profile retrieved successfully', {
        user: userWithoutPassword,
      });
    } catch (error: any) {
      Logger.error('Get profile error', {
        error: error.message,
        userId: req.user?.id,
      });
      
      ResponseUtil.internalServerError(res, 'Failed to retrieve profile');
    }
  }
}
