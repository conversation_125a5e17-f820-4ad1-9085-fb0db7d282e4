import { Request, Response, NextFunction } from 'express';
import { Schema } from 'joi';
import { ResponseUtil } from '@/utils/response';

export const validateRequest = (schema: Schema, property: 'body' | 'query' | 'params' = 'body') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error } = schema.validate(req[property], {
      abortEarly: false,
      allowUnknown: false,
      stripUnknown: true,
    });

    if (error) {
      const errorMessage = error.details
        .map(detail => detail.message)
        .join(', ');
      
      ResponseUtil.badRequest(res, 'Validation failed', errorMessage);
      return;
    }

    next();
  };
};

export const validateBody = (schema: Schema) => validateRequest(schema, 'body');
export const validateQuery = (schema: Schema) => validateRequest(schema, 'query');
export const validateParams = (schema: Schema) => validateRequest(schema, 'params');
