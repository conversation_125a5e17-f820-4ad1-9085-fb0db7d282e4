import dotenv from 'dotenv';

dotenv.config();

export const config = {
  port: parseInt(process.env.PORT || '3000'),
  nodeEnv: process.env.NODE_ENV || 'development',
  
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'password',
    name: process.env.DB_NAME || 'lendsqr_wallet',
  },
  
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-here',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  },
  
  karma: {
    apiUrl: process.env.KARMA_API_URL || 'https://adjutor.lendsqr.com/v2/verification/karma',
    apiKey: process.env.KARMA_API_KEY || '',
  },
  
  app: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12'),
    maxTransferAmount: parseInt(process.env.MAX_TRANSFER_AMOUNT || '1000000'),
    minTransferAmount: parseInt(process.env.MIN_TRANSFER_AMOUNT || '1'),
  },
};
