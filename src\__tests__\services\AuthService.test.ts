import { AuthService } from '@/services/AuthService';
import { User } from '@/models/User';
import { Wallet } from '@/models/Wallet';
import { KarmaService } from '@/services/KarmaService';
import { HelperUtil } from '@/utils/helpers';

// Mock KarmaService
jest.mock('@/services/KarmaService');
const mockKarmaService = KarmaService as jest.Mocked<typeof KarmaService>;

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockKarmaService.isUserBlacklisted.mockResolvedValue(false);
  });

  describe('registerUser', () => {
    const validUserData = {
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      phoneNumber: '+*************',
      password: 'Password123!',
    };

    it('should register a new user successfully', async () => {
      const result = await AuthService.registerUser(validUserData);

      expect(result.user).toBeDefined();
      expect(result.token).toBeDefined();
      expect(result.user.email).toBe(validUserData.email.toLowerCase());
      expect(result.user.first_name).toBe(validUserData.firstName);
      expect(result.user.last_name).toBe(validUserData.lastName);

      // Verify wallet was created
      const wallet = await Wallet.findByUserId(result.user.id);
      expect(wallet).toBeDefined();
      expect(wallet?.balance).toBe('0.00');
    });

    it('should throw error if email already exists', async () => {
      // Create a user first
      await AuthService.registerUser(validUserData);

      // Try to register with same email
      await expect(
        AuthService.registerUser(validUserData)
      ).rejects.toThrow('User with this email already exists');
    });

    it('should throw error if phone number already exists', async () => {
      // Create a user first
      await AuthService.registerUser(validUserData);

      // Try to register with same phone number but different email
      await expect(
        AuthService.registerUser({
          ...validUserData,
          email: '<EMAIL>',
        })
      ).rejects.toThrow('User with this phone number already exists');
    });

    it('should throw error if user is blacklisted', async () => {
      mockKarmaService.isUserBlacklisted.mockResolvedValue(true);

      await expect(
        AuthService.registerUser(validUserData)
      ).rejects.toThrow('User cannot be onboarded due to blacklist status');
    });

    it('should sanitize email and phone number', async () => {
      const userData = {
        ...validUserData,
        email: '  <EMAIL>  ',
        phoneNumber: '+234 ************',
      };

      const result = await AuthService.registerUser(userData);

      expect(result.user.email).toBe('<EMAIL>');
      expect(result.user.phone_number).toBe('+*************');
    });
  });

  describe('loginUser', () => {
    let testUser: any;

    beforeEach(async () => {
      const userData = {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        phoneNumber: '+*************',
        password: 'Password123!',
      };
      const result = await AuthService.registerUser(userData);
      testUser = result.user;
    });

    it('should login user successfully', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'Password123!',
      };

      const result = await AuthService.loginUser(loginData);

      expect(result.user).toBeDefined();
      expect(result.token).toBeDefined();
      expect(result.user.id).toBe(testUser.id);
    });

    it('should throw error for invalid email', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'Password123!',
      };

      await expect(
        AuthService.loginUser(loginData)
      ).rejects.toThrow('Invalid email or password');
    });

    it('should throw error for invalid password', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'WrongPassword',
      };

      await expect(
        AuthService.loginUser(loginData)
      ).rejects.toThrow('Invalid email or password');
    });

    it('should throw error if user is not active', async () => {
      // Deactivate user
      await User.update(testUser.id, { is_active: false });

      const loginData = {
        email: '<EMAIL>',
        password: 'Password123!',
      };

      await expect(
        AuthService.loginUser(loginData)
      ).rejects.toThrow('Account is deactivated');
    });

    it('should throw error if user is blacklisted', async () => {
      // Blacklist user
      await User.update(testUser.id, { is_blacklisted: true });

      const loginData = {
        email: '<EMAIL>',
        password: 'Password123!',
      };

      await expect(
        AuthService.loginUser(loginData)
      ).rejects.toThrow('Account is restricted');
    });
  });

  describe('getUserById', () => {
    it('should return user if exists', async () => {
      const userData = {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        phoneNumber: '+*************',
        password: 'Password123!',
      };
      const result = await AuthService.registerUser(userData);

      const user = await AuthService.getUserById(result.user.id);
      expect(user).toBeDefined();
      expect(user?.id).toBe(result.user.id);
    });

    it('should return null if user does not exist', async () => {
      const user = await AuthService.getUserById('nonexistent-id');
      expect(user).toBeNull();
    });
  });
});
