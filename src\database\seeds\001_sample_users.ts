import { Knex } from 'knex';
import { <PERSON><PERSON><PERSON><PERSON> } from '@/utils/helpers';

export async function seed(knex: Knex): Promise<void> {
  // Deletes ALL existing entries
  await knex('transactions').del();
  await knex('wallets').del();
  await knex('users').del();

  const hashedPassword = await HelperUtil.hashPassword('Password123!');

  // Insert sample users
  const users = [
    {
      id: HelperUtil.generateId(),
      email: '<EMAIL>',
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
      phone_number: '+2348123456789',
      password_hash: hashedPassword,
      is_blacklisted: false,
      is_active: true,
    },
    {
      id: HelperUtil.generateId(),
      email: '<EMAIL>',
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      phone_number: '+2348987654321',
      password_hash: hashedPassword,
      is_blacklisted: false,
      is_active: true,
    },
  ];

  await knex('users').insert(users);

  // Insert corresponding wallets
  const wallets = users.map(user => ({
    id: HelperUtil.generateId(),
    user_id: user.id,
    balance: 0.00,
    currency: 'NGN',
    is_active: true,
  }));

  await knex('wallets').insert(wallets);
}
