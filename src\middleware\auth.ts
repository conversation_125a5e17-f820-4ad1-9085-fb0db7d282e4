import { Request, Response, NextFunction } from 'express';
import { HelperUtil } from '@/utils/helpers';
import { ResponseUtil } from '@/utils/response';
import { AuthService } from '@/services/AuthService';
import { JwtPayload } from '@/types';
import { Logger } from '@/utils/logger';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
      };
    }
  }
}

export const authenticateToken = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      ResponseUtil.unauthorized(res, 'Access token is required');
      return;
    }

    // Verify token
    const decoded: JwtPayload = HelperUtil.verifyToken(token);

    // Get user from database to ensure they still exist and are active
    const user = await AuthService.getUserById(decoded.userId);
    if (!user) {
      ResponseUtil.unauthorized(res, 'Invalid token - user not found');
      return;
    }

    if (!user.is_active) {
      ResponseUtil.unauthorized(res, 'Account is deactivated');
      return;
    }

    if (user.is_blacklisted) {
      ResponseUtil.forbidden(res, 'Account is restricted');
      return;
    }

    // Add user info to request object
    req.user = {
      id: user.id,
      email: user.email,
    };

    next();
  } catch (error: any) {
    Logger.error('Authentication error', {
      error: error.message,
      path: req.path,
      method: req.method,
    });

    if (error.name === 'JsonWebTokenError') {
      ResponseUtil.unauthorized(res, 'Invalid token');
      return;
    }

    if (error.name === 'TokenExpiredError') {
      ResponseUtil.unauthorized(res, 'Token has expired');
      return;
    }

    ResponseUtil.unauthorized(res, 'Authentication failed');
  }
};

export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const decoded: JwtPayload = HelperUtil.verifyToken(token);
      const user = await AuthService.getUserById(decoded.userId);
      
      if (user && user.is_active && !user.is_blacklisted) {
        req.user = {
          id: user.id,
          email: user.email,
        };
      }
    }

    next();
  } catch (error) {
    // For optional auth, we don't return errors, just continue without user
    next();
  }
};
