import { Router } from 'express';
import { WalletController } from '@/controllers/WalletController';
import { validateBody } from '@/middleware/validation';
import { authenticateToken } from '@/middleware/auth';
import { fundWalletSchema, transferSchema, withdrawSchema } from '@/utils/validation';

const router = Router();

// All wallet routes require authentication
router.use(authenticateToken);

/**
 * @route   GET /api/wallet
 * @desc    Get user wallet details
 * @access  Private
 */
router.get('/', WalletController.getWallet);

/**
 * @route   GET /api/wallet/balance
 * @desc    Get wallet balance
 * @access  Private
 */
router.get('/balance', WalletController.getBalance);

/**
 * @route   POST /api/wallet/fund
 * @desc    Fund wallet
 * @access  Private
 */
router.post('/fund', validateBody(fundWalletSchema), WalletController.fundWallet);

/**
 * @route   POST /api/wallet/transfer
 * @desc    Transfer funds to another user
 * @access  Private
 */
router.post('/transfer', validateBody(transferSchema), WalletController.transferFunds);

/**
 * @route   POST /api/wallet/withdraw
 * @desc    Withdraw funds from wallet
 * @access  Private
 */
router.post('/withdraw', validateBody(withdrawSchema), WalletController.withdrawFunds);

/**
 * @route   GET /api/wallet/transactions
 * @desc    Get wallet transactions
 * @access  Private
 */
router.get('/transactions', WalletController.getTransactions);

export default router;
