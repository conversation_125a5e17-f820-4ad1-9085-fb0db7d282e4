import request from 'supertest';
import app from '@/app';
import { AuthService } from '@/services/AuthService';
import { WalletService } from '@/services/WalletService';
import { KarmaService } from '@/services/KarmaService';

// Mock services
jest.mock('@/services/KarmaService');
const mockKarmaService = KarmaService as jest.Mocked<typeof KarmaService>;

describe('WalletController', () => {
  let token: string;
  let userId: string;

  beforeEach(async () => {
    jest.clearAllMocks();
    mockKarmaService.isUserBlacklisted.mockResolvedValue(false);

    const userData = {
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      phoneNumber: '+2348123456789',
      password: 'Password123!',
    };
    const result = await AuthService.registerUser(userData);
    token = result.token;
    userId = result.user.id;
  });

  describe('GET /api/wallet', () => {
    it('should get wallet successfully', async () => {
      const response = await request(app)
        .get('/api/wallet')
        .set('Authorization', `Bearer ${token}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Wallet retrieved successfully');
      expect(response.body.data.wallet).toBeDefined();
      expect(response.body.data.wallet.user_id).toBe(userId);
    });

    it('should return 401 without token', async () => {
      const response = await request(app)
        .get('/api/wallet');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/wallet/balance', () => {
    it('should get balance successfully', async () => {
      const response = await request(app)
        .get('/api/wallet/balance')
        .set('Authorization', `Bearer ${token}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Balance retrieved successfully');
      expect(response.body.data.balance).toBeDefined();
      expect(response.body.data.formattedBalance).toBeDefined();
    });
  });

  describe('POST /api/wallet/fund', () => {
    it('should fund wallet successfully', async () => {
      const fundData = {
        amount: 1000,
        description: 'Test funding',
      };

      const response = await request(app)
        .post('/api/wallet/fund')
        .set('Authorization', `Bearer ${token}`)
        .send(fundData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Wallet funded successfully');
      expect(response.body.data.transaction).toBeDefined();
      expect(response.body.data.wallet).toBeDefined();
      expect(response.body.data.transaction.amount).toBe(1000);
    });

    it('should return 400 for invalid amount', async () => {
      const fundData = {
        amount: -100,
      };

      const response = await request(app)
        .post('/api/wallet/fund')
        .set('Authorization', `Bearer ${token}`)
        .send(fundData);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should return 400 for validation errors', async () => {
      const response = await request(app)
        .post('/api/wallet/fund')
        .set('Authorization', `Bearer ${token}`)
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Validation failed');
    });
  });

  describe('POST /api/wallet/transfer', () => {
    let recipientToken: string;

    beforeEach(async () => {
      // Create recipient user
      const recipientData = {
        email: '<EMAIL>',
        firstName: 'Recipient',
        lastName: 'User',
        phoneNumber: '+2348987654321',
        password: 'Password123!',
      };
      const recipientResult = await AuthService.registerUser(recipientData);
      recipientToken = recipientResult.token;

      // Fund sender wallet
      await WalletService.fundWallet(userId, { amount: 2000 });
    });

    it('should transfer funds successfully', async () => {
      const transferData = {
        recipientEmail: '<EMAIL>',
        amount: 1000,
        description: 'Test transfer',
      };

      const response = await request(app)
        .post('/api/wallet/transfer')
        .set('Authorization', `Bearer ${token}`)
        .send(transferData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Transfer completed successfully');
      expect(response.body.data.transaction).toBeDefined();
      expect(response.body.data.senderWallet).toBeDefined();
      expect(response.body.data.recipientWallet).toBeDefined();
    });

    it('should return 400 for insufficient balance', async () => {
      const transferData = {
        recipientEmail: '<EMAIL>',
        amount: 5000, // More than available
      };

      const response = await request(app)
        .post('/api/wallet/transfer')
        .set('Authorization', `Bearer ${token}`)
        .send(transferData);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Insufficient balance');
    });

    it('should return 400 for self-transfer', async () => {
      const transferData = {
        recipientEmail: '<EMAIL>', // Same as sender
        amount: 1000,
      };

      const response = await request(app)
        .post('/api/wallet/transfer')
        .set('Authorization', `Bearer ${token}`)
        .send(transferData);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Cannot transfer to yourself');
    });
  });

  describe('POST /api/wallet/withdraw', () => {
    beforeEach(async () => {
      // Fund wallet first
      await WalletService.fundWallet(userId, { amount: 2000 });
    });

    it('should withdraw funds successfully', async () => {
      const withdrawData = {
        amount: 1000,
        description: 'Test withdrawal',
      };

      const response = await request(app)
        .post('/api/wallet/withdraw')
        .set('Authorization', `Bearer ${token}`)
        .send(withdrawData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Withdrawal completed successfully');
      expect(response.body.data.transaction).toBeDefined();
      expect(response.body.data.wallet).toBeDefined();
    });

    it('should return 400 for insufficient balance', async () => {
      const withdrawData = {
        amount: 5000, // More than available
      };

      const response = await request(app)
        .post('/api/wallet/withdraw')
        .set('Authorization', `Bearer ${token}`)
        .send(withdrawData);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Insufficient balance');
    });
  });

  describe('GET /api/wallet/transactions', () => {
    beforeEach(async () => {
      // Create some transactions
      await WalletService.fundWallet(userId, { amount: 1000 });
      await WalletService.withdrawFunds(userId, { amount: 500 });
    });

    it('should get transactions successfully', async () => {
      const response = await request(app)
        .get('/api/wallet/transactions')
        .set('Authorization', `Bearer ${token}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Transactions retrieved successfully');
      expect(response.body.data.transactions).toBeDefined();
      expect(Array.isArray(response.body.data.transactions)).toBe(true);
      expect(response.body.data.count).toBeDefined();
    });

    it('should respect limit parameter', async () => {
      const response = await request(app)
        .get('/api/wallet/transactions?limit=1')
        .set('Authorization', `Bearer ${token}`);

      expect(response.status).toBe(200);
      expect(response.body.data.transactions.length).toBeLessThanOrEqual(1);
    });

    it('should return 400 for limit exceeding 100', async () => {
      const response = await request(app)
        .get('/api/wallet/transactions?limit=150')
        .set('Authorization', `Bearer ${token}`);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Limit cannot exceed 100');
    });
  });
});
