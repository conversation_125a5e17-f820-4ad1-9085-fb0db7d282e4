import { WalletService } from '@/services/WalletService';
import { createTestUserWithWallet } from '../helpers/testHelpers';
import { TransactionStatus, TransactionType } from '@/types';

describe('WalletService', () => {
  describe('getUserWallet', () => {
    it('should return user wallet', async () => {
      const { user, wallet } = await createTestUserWithWallet();

      const result = await WalletService.getUserWallet(user.id);

      expect(result).toBeDefined();
      expect(result.id).toBe(wallet.id);
      expect(result.user_id).toBe(user.id);
    });

    it('should throw error if wallet not found', async () => {
      await expect(
        WalletService.getUserWallet('nonexistent-user-id')
      ).rejects.toThrow('Wallet not found for user');
    });
  });

  describe('fundWallet', () => {
    it('should fund wallet successfully', async () => {
      const { user } = await createTestUserWithWallet();
      const fundData = {
        amount: 1000,
        description: 'Test funding',
      };

      const result = await WalletService.fundWallet(user.id, fundData);

      expect(result.transaction).toBeDefined();
      expect(result.wallet).toBeDefined();
      expect(result.transaction.amount).toBe(1000);
      expect(result.transaction.type).toBe(TransactionType.CREDIT);
      expect(result.transaction.status).toBe(TransactionStatus.COMPLETED);
      expect(parseFloat(result.wallet.balance.toString())).toBe(1000);
    });

    it('should throw error for invalid amount', async () => {
      const { user } = await createTestUserWithWallet();
      const fundData = {
        amount: -100, // Invalid negative amount
      };

      await expect(
        WalletService.fundWallet(user.id, fundData)
      ).rejects.toThrow('Amount must be between');
    });

    it('should throw error if wallet not found', async () => {
      const fundData = {
        amount: 1000,
      };

      await expect(
        WalletService.fundWallet('nonexistent-user-id', fundData)
      ).rejects.toThrow('Wallet not found');
    });
  });

  describe('transferFunds', () => {
    it('should transfer funds successfully', async () => {
      const { user: sender } = await createTestUserWithWallet(
        { email: '<EMAIL>' },
        { balance: 2000 }
      );
      const { user: recipient } = await createTestUserWithWallet(
        { email: '<EMAIL>', phoneNumber: '+2348987654321' },
        { balance: 500 }
      );

      const transferData = {
        recipientEmail: '<EMAIL>',
        amount: 1000,
        description: 'Test transfer',
      };

      const result = await WalletService.transferFunds(sender.id, transferData);

      expect(result.transaction).toBeDefined();
      expect(result.senderWallet).toBeDefined();
      expect(result.recipientWallet).toBeDefined();
      expect(result.transaction.amount).toBe(1000);
      expect(result.transaction.type).toBe(TransactionType.TRANSFER);
      expect(result.transaction.status).toBe(TransactionStatus.COMPLETED);
      expect(parseFloat(result.senderWallet.balance.toString())).toBe(1000);
      expect(parseFloat(result.recipientWallet.balance.toString())).toBe(1500);
    });

    it('should throw error for insufficient balance', async () => {
      const { user: sender } = await createTestUserWithWallet(
        { email: '<EMAIL>' },
        { balance: 500 }
      );
      const { user: recipient } = await createTestUserWithWallet(
        { email: '<EMAIL>', phoneNumber: '+2348987654321' }
      );

      const transferData = {
        recipientEmail: '<EMAIL>',
        amount: 1000, // More than available balance
      };

      await expect(
        WalletService.transferFunds(sender.id, transferData)
      ).rejects.toThrow('Insufficient balance');
    });

    it('should throw error for self-transfer', async () => {
      const { user } = await createTestUserWithWallet(
        { email: '<EMAIL>' },
        { balance: 2000 }
      );

      const transferData = {
        recipientEmail: '<EMAIL>',
        amount: 1000,
      };

      await expect(
        WalletService.transferFunds(user.id, transferData)
      ).rejects.toThrow('Cannot transfer to yourself');
    });

    it('should throw error if recipient not found', async () => {
      const { user: sender } = await createTestUserWithWallet(
        { email: '<EMAIL>' },
        { balance: 2000 }
      );

      const transferData = {
        recipientEmail: '<EMAIL>',
        amount: 1000,
      };

      await expect(
        WalletService.transferFunds(sender.id, transferData)
      ).rejects.toThrow('Recipient not found');
    });

    it('should throw error if recipient is blacklisted', async () => {
      const { user: sender } = await createTestUserWithWallet(
        { email: '<EMAIL>' },
        { balance: 2000 }
      );
      const { user: recipient } = await createTestUserWithWallet(
        { 
          email: '<EMAIL>', 
          phoneNumber: '+2348987654321',
          isBlacklisted: true 
        }
      );

      const transferData = {
        recipientEmail: '<EMAIL>',
        amount: 1000,
      };

      await expect(
        WalletService.transferFunds(sender.id, transferData)
      ).rejects.toThrow('Cannot transfer to blacklisted user');
    });
  });

  describe('withdrawFunds', () => {
    it('should withdraw funds successfully', async () => {
      const { user } = await createTestUserWithWallet(
        {},
        { balance: 2000 }
      );

      const withdrawData = {
        amount: 1000,
        description: 'Test withdrawal',
      };

      const result = await WalletService.withdrawFunds(user.id, withdrawData);

      expect(result.transaction).toBeDefined();
      expect(result.wallet).toBeDefined();
      expect(result.transaction.amount).toBe(1000);
      expect(result.transaction.type).toBe(TransactionType.DEBIT);
      expect(result.transaction.status).toBe(TransactionStatus.COMPLETED);
      expect(parseFloat(result.wallet.balance.toString())).toBe(1000);
    });

    it('should throw error for insufficient balance', async () => {
      const { user } = await createTestUserWithWallet(
        {},
        { balance: 500 }
      );

      const withdrawData = {
        amount: 1000, // More than available balance
      };

      await expect(
        WalletService.withdrawFunds(user.id, withdrawData)
      ).rejects.toThrow('Insufficient balance');
    });

    it('should throw error if wallet not found', async () => {
      const withdrawData = {
        amount: 1000,
      };

      await expect(
        WalletService.withdrawFunds('nonexistent-user-id', withdrawData)
      ).rejects.toThrow('Wallet not found');
    });
  });

  describe('getWalletBalance', () => {
    it('should return wallet balance', async () => {
      const { user } = await createTestUserWithWallet(
        {},
        { balance: 1500.50 }
      );

      const balance = await WalletService.getWalletBalance(user.id);

      expect(balance).toBe(1500.50);
    });

    it('should throw error if wallet not found', async () => {
      await expect(
        WalletService.getWalletBalance('nonexistent-user-id')
      ).rejects.toThrow('Wallet not found');
    });
  });
});
