import { User } from '@/models/User';
import { Wallet } from '@/models/Wallet';
import { HelperUtil } from '@/utils/helpers';

export const createTestUser = async (overrides: any = {}) => {
  const userData = {
    id: HelperUtil.generateId(),
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    phoneNumber: '+2348123456789',
    passwordHash: await HelperUtil.hashPassword('Password123!'),
    isBlacklisted: false,
    ...overrides,
  };

  return User.createUser(userData);
};

export const createTestWallet = async (userId: string, overrides: any = {}) => {
  const walletData = {
    id: HelperUtil.generateId(),
    userId,
    balance: 0,
    currency: 'NGN',
    ...overrides,
  };

  return Wallet.createWallet(walletData);
};

export const createTestUserWithWallet = async (userOverrides: any = {}, walletOverrides: any = {}) => {
  const user = await createTestUser(userOverrides);
  const wallet = await createTestWallet(user.id, walletOverrides);
  
  return { user, wallet };
};
