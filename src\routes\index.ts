import { Router } from 'express';
import authRoutes from './auth';
import walletRoutes from './wallet';

const router = Router();

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Lendsqr Wallet Service is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  });
});

// API routes
router.use('/auth', authRoutes);
router.use('/wallet', walletRoutes);

export default router;
