import axios from 'axios';
import { config } from '@/config';
import { KarmaBlacklistResponse } from '@/types';
import { Logger } from '@/utils/logger';

export class KarmaService {
  private static readonly API_URL = config.karma.apiUrl;
  private static readonly API_KEY = config.karma.apiKey;

  static async checkBlacklist(identity: string): Promise<boolean> {
    try {
      if (!this.API_KEY) {
        Logger.warn('Karma API key not configured, skipping blacklist check');
        return false;
      }

      const response = await axios.get<KarmaBlacklistResponse>(
        `${this.API_URL}/${identity}`,
        {
          headers: {
            'Authorization': `Bearer ${this.API_KEY}`,
            'Content-Type': 'application/json',
          },
          timeout: 10000, // 10 seconds timeout
        }
      );

      if (response.data.status === 'success' && response.data.data.length > 0) {
        Logger.info(`Identity ${identity} found in Karma blacklist`, {
          reasons: response.data.data.map(item => item.reason),
        });
        return true;
      }

      Logger.info(`Identity ${identity} not found in Karma blacklist`);
      return false;
    } catch (error: any) {
      Logger.error('Error checking Karma blacklist', {
        identity,
        error: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });

      // If API is down or returns error, we don't block user registration
      // but log the error for monitoring
      if (error.response?.status === 404) {
        // 404 means identity not found in blacklist
        return false;
      }

      // For other errors (network, timeout, etc.), we assume not blacklisted
      // to avoid blocking legitimate users due to API issues
      return false;
    }
  }

  static async checkMultipleIdentities(identities: string[]): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};

    // Check identities in parallel but with a reasonable concurrency limit
    const promises = identities.map(async (identity) => {
      const isBlacklisted = await this.checkBlacklist(identity);
      results[identity] = isBlacklisted;
    });

    await Promise.all(promises);
    return results;
  }

  static async isUserBlacklisted(email: string, phoneNumber: string): Promise<boolean> {
    try {
      const identities = [email, phoneNumber];
      const results = await this.checkMultipleIdentities(identities);

      // User is blacklisted if any of their identities are blacklisted
      return Object.values(results).some(isBlacklisted => isBlacklisted);
    } catch (error: any) {
      Logger.error('Error checking user blacklist status', {
        email,
        phoneNumber,
        error: error.message,
      });

      // Default to not blacklisted if there's an error
      return false;
    }
  }
}
