import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { config } from '@/config';
import { JwtPayload } from '@/types';

export class HelperUtil {
  static generateId(): string {
    return uuidv4();
  }

  static generateReference(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `TXN_${timestamp}_${random}`;
  }

  static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, config.app.bcryptRounds);
  }

  static async comparePassword(password: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword);
  }

  static generateToken(payload: { userId: string; email: string }): string {
    return jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn,
    });
  }

  static verifyToken(token: string): JwtPayload {
    return jwt.verify(token, config.jwt.secret) as JwtPayload;
  }

  static formatCurrency(amount: number, currency: string = 'NGN'): string {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency,
    }).format(amount);
  }

  static isValidAmount(amount: number): boolean {
    return amount >= config.app.minTransferAmount && amount <= config.app.maxTransferAmount;
  }

  static sanitizeEmail(email: string): string {
    return email.toLowerCase().trim();
  }

  static sanitizePhoneNumber(phoneNumber: string): string {
    return phoneNumber.replace(/\s+/g, '').replace(/[^\d+]/g, '');
  }
}
