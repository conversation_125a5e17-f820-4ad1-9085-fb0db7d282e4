import { BaseModel } from './BaseModel';
import { Transaction as TransactionType, TransactionType as TxnType, TransactionStatus } from '@/types';

export class Transaction extends BaseModel {
  protected static tableName = 'transactions';

  static async createTransaction(transactionData: {
    id: string;
    fromWalletId?: string | null;
    toWalletId?: string | null;
    amount: number;
    type: TxnType;
    status?: TransactionStatus;
    reference: string;
    description?: string;
    metadata?: Record<string, any>;
  }): Promise<TransactionType> {
    const transaction = {
      id: transactionData.id,
      from_wallet_id: transactionData.fromWalletId || null,
      to_wallet_id: transactionData.toWalletId || null,
      amount: transactionData.amount,
      type: transactionData.type,
      status: transactionData.status || TransactionStatus.PENDING,
      reference: transactionData.reference,
      description: transactionData.description || null,
      metadata: transactionData.metadata ? JSON.stringify(transactionData.metadata) : null,
    };

    return this.create(transaction);
  }

  static async findByReference(reference: string): Promise<TransactionType | null> {
    return this.query.where('reference', reference).first();
  }

  static async findByWalletId(walletId: string, limit: number = 50): Promise<TransactionType[]> {
    return this.query
      .where('from_wallet_id', walletId)
      .orWhere('to_wallet_id', walletId)
      .orderBy('created_at', 'desc')
      .limit(limit);
  }

  static async findByFromWalletId(walletId: string, limit: number = 50): Promise<TransactionType[]> {
    return this.query
      .where('from_wallet_id', walletId)
      .orderBy('created_at', 'desc')
      .limit(limit);
  }

  static async findByToWalletId(walletId: string, limit: number = 50): Promise<TransactionType[]> {
    return this.query
      .where('to_wallet_id', walletId)
      .orderBy('created_at', 'desc')
      .limit(limit);
  }

  static async updateStatus(id: string, status: TransactionStatus): Promise<TransactionType> {
    return this.update(id, { status });
  }

  static async getTransactionsByStatus(status: TransactionStatus): Promise<TransactionType[]> {
    return this.query.where('status', status).select('*');
  }

  static async getTransactionsByType(type: TxnType): Promise<TransactionType[]> {
    return this.query.where('type', type).select('*');
  }

  static async getPendingTransactions(): Promise<TransactionType[]> {
    return this.getTransactionsByStatus(TransactionStatus.PENDING);
  }

  static async getCompletedTransactions(): Promise<TransactionType[]> {
    return this.getTransactionsByStatus(TransactionStatus.COMPLETED);
  }

  static async getFailedTransactions(): Promise<TransactionType[]> {
    return this.getTransactionsByStatus(TransactionStatus.FAILED);
  }

  static async referenceExists(reference: string): Promise<boolean> {
    const transaction = await this.findByReference(reference);
    return !!transaction;
  }
}
