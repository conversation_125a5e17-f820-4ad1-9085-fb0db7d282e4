import app from './app';
import { config } from '@/config';
import { Logger } from '@/utils/logger';
import db from '@/config/database';

const startServer = async (): Promise<void> => {
  try {
    // Test database connection
    await db.raw('SELECT 1');
    Logger.info('Database connection established successfully');

    // Start server
    const server = app.listen(config.port, () => {
      Logger.info(`Server is running on port ${config.port}`);
      Logger.info(`Environment: ${config.nodeEnv}`);
      Logger.info(`Health check: http://localhost:${config.port}/api/health`);
    });

    // Graceful shutdown
    const gracefulShutdown = (signal: string) => {
      Logger.info(`Received ${signal}. Starting graceful shutdown...`);
      
      server.close(async () => {
        Logger.info('HTTP server closed');
        
        try {
          await db.destroy();
          Logger.info('Database connection closed');
          process.exit(0);
        } catch (error) {
          Logger.error('Error during database shutdown', error);
          process.exit(1);
        }
      });
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    Logger.error('Failed to start server', error);
    process.exit(1);
  }
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  Logger.error('Unhandled Rejection at:', { promise, reason });
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  Logger.error('Uncaught Exception:', error);
  process.exit(1);
});

startServer();
