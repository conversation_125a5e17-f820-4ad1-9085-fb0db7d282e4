import { BaseModel } from './BaseModel';
import { User as UserType } from '@/types';

export class User extends BaseModel {
  protected static tableName = 'users';

  static async findByEmail(email: string): Promise<UserType | null> {
    return this.query.where('email', email).first();
  }

  static async findByPhoneNumber(phoneNumber: string): Promise<UserType | null> {
    return this.query.where('phone_number', phoneNumber).first();
  }

  static async createUser(userData: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    phoneNumber: string;
    passwordHash: string;
    isBlacklisted?: boolean;
  }): Promise<UserType> {
    const user = {
      id: userData.id,
      email: userData.email,
      first_name: userData.firstName,
      last_name: userData.lastName,
      phone_number: userData.phoneNumber,
      password_hash: userData.passwordHash,
      is_blacklisted: userData.isBlacklisted || false,
      is_active: true,
    };

    return this.create(user);
  }

  static async updateBlacklistStatus(id: string, isBlacklisted: boolean): Promise<UserType> {
    return this.update(id, { is_blacklisted: isBlacklisted });
  }

  static async updateActiveStatus(id: string, isActive: boolean): Promise<UserType> {
    return this.update(id, { is_active: isActive });
  }

  static async getActiveUsers(): Promise<UserType[]> {
    return this.query.where('is_active', true).select('*');
  }

  static async getBlacklistedUsers(): Promise<UserType[]> {
    return this.query.where('is_blacklisted', true).select('*');
  }

  static async emailExists(email: string): Promise<boolean> {
    const user = await this.findByEmail(email);
    return !!user;
  }

  static async phoneNumberExists(phoneNumber: string): Promise<boolean> {
    const user = await this.findByPhoneNumber(phoneNumber);
    return !!user;
  }
}
