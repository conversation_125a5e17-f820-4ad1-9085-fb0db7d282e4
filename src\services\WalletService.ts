import { Wallet } from '@/models/Wallet';
import { User } from '@/models/User';
import { Transaction } from '@/models/Transaction';
import { HelperUtil } from '@/utils/helpers';
import { Logger } from '@/utils/logger';
import { 
  Wallet as WalletType, 
  Transaction as TransactionType,
  TransactionType as TxnType,
  TransactionStatus,
  FundWalletRequest,
  TransferRequest,
  WithdrawRequest
} from '@/types';
import db from '@/config/database';
import { config } from '@/config';

export class WalletService {
  static async getUserWallet(userId: string): Promise<WalletType> {
    const wallet = await Wallet.findByUserId(userId);
    if (!wallet) {
      throw new Error('Wallet not found for user');
    }
    return wallet;
  }

  static async fundWallet(userId: string, fundData: FundWalletRequest): Promise<{
    transaction: TransactionType;
    wallet: WalletType;
  }> {
    const { amount, description } = fundData;

    // Validate amount
    if (!HelperUtil.isValidAmount(amount)) {
      throw new Error(`Amount must be between ${config.app.minTransferAmount} and ${config.app.maxTransferAmount}`);
    }

    const trx = await db.transaction();
    try {
      // Get user wallet
      const wallet = await Wallet.findByUserId(userId);
      if (!wallet) {
        throw new Error('Wallet not found');
      }

      if (!wallet.is_active) {
        throw new Error('Wallet is not active');
      }

      // Create transaction record
      const transactionId = HelperUtil.generateId();
      const reference = HelperUtil.generateReference();
      
      const transaction = await Transaction.createTransaction({
        id: transactionId,
        toWalletId: wallet.id,
        amount,
        type: TxnType.CREDIT,
        status: TransactionStatus.PENDING,
        reference,
        description: description || 'Wallet funding',
        metadata: {
          fundingType: 'manual',
          userId,
        },
      });

      // Update wallet balance
      const updatedWallet = await Wallet.incrementBalance(wallet.id, amount);

      // Update transaction status to completed
      await Transaction.updateStatus(transactionId, TransactionStatus.COMPLETED);

      await trx.commit();

      Logger.info('Wallet funded successfully', {
        userId,
        walletId: wallet.id,
        amount,
        reference,
        newBalance: updatedWallet.balance,
      });

      return {
        transaction: { ...transaction, status: TransactionStatus.COMPLETED },
        wallet: updatedWallet,
      };
    } catch (error) {
      await trx.rollback();
      Logger.error('Error funding wallet', {
        userId,
        amount,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  static async transferFunds(userId: string, transferData: TransferRequest): Promise<{
    transaction: TransactionType;
    senderWallet: WalletType;
    recipientWallet: WalletType;
  }> {
    const { recipientEmail, amount, description } = transferData;

    // Validate amount
    if (!HelperUtil.isValidAmount(amount)) {
      throw new Error(`Amount must be between ${config.app.minTransferAmount} and ${config.app.maxTransferAmount}`);
    }

    const sanitizedRecipientEmail = HelperUtil.sanitizeEmail(recipientEmail);

    const trx = await db.transaction();
    try {
      // Get sender wallet
      const senderWallet = await Wallet.findByUserId(userId);
      if (!senderWallet) {
        throw new Error('Sender wallet not found');
      }

      if (!senderWallet.is_active) {
        throw new Error('Sender wallet is not active');
      }

      // Check if sender has sufficient balance
      if (await Wallet.hasInsufficientBalance(senderWallet.id, amount)) {
        throw new Error('Insufficient balance');
      }

      // Get recipient user and wallet
      const recipientUser = await User.findByEmail(sanitizedRecipientEmail);
      if (!recipientUser) {
        throw new Error('Recipient not found');
      }

      if (!recipientUser.is_active) {
        throw new Error('Recipient account is not active');
      }

      if (recipientUser.is_blacklisted) {
        throw new Error('Cannot transfer to blacklisted user');
      }

      // Prevent self-transfer
      if (recipientUser.id === userId) {
        throw new Error('Cannot transfer to yourself');
      }

      const recipientWallet = await Wallet.findByUserId(recipientUser.id);
      if (!recipientWallet) {
        throw new Error('Recipient wallet not found');
      }

      if (!recipientWallet.is_active) {
        throw new Error('Recipient wallet is not active');
      }

      // Create transaction record
      const transactionId = HelperUtil.generateId();
      const reference = HelperUtil.generateReference();
      
      const transaction = await Transaction.createTransaction({
        id: transactionId,
        fromWalletId: senderWallet.id,
        toWalletId: recipientWallet.id,
        amount,
        type: TxnType.TRANSFER,
        status: TransactionStatus.PENDING,
        reference,
        description: description || `Transfer to ${recipientUser.first_name} ${recipientUser.last_name}`,
        metadata: {
          senderId: userId,
          recipientId: recipientUser.id,
          recipientEmail: sanitizedRecipientEmail,
        },
      });

      // Update balances
      const updatedSenderWallet = await Wallet.decrementBalance(senderWallet.id, amount);
      const updatedRecipientWallet = await Wallet.incrementBalance(recipientWallet.id, amount);

      // Update transaction status to completed
      await Transaction.updateStatus(transactionId, TransactionStatus.COMPLETED);

      await trx.commit();

      Logger.info('Transfer completed successfully', {
        senderId: userId,
        recipientId: recipientUser.id,
        amount,
        reference,
        senderNewBalance: updatedSenderWallet.balance,
        recipientNewBalance: updatedRecipientWallet.balance,
      });

      return {
        transaction: { ...transaction, status: TransactionStatus.COMPLETED },
        senderWallet: updatedSenderWallet,
        recipientWallet: updatedRecipientWallet,
      };
    } catch (error) {
      await trx.rollback();
      Logger.error('Error transferring funds', {
        userId,
        recipientEmail: sanitizedRecipientEmail,
        amount,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  static async withdrawFunds(userId: string, withdrawData: WithdrawRequest): Promise<{
    transaction: TransactionType;
    wallet: WalletType;
  }> {
    const { amount, description } = withdrawData;

    // Validate amount
    if (!HelperUtil.isValidAmount(amount)) {
      throw new Error(`Amount must be between ${config.app.minTransferAmount} and ${config.app.maxTransferAmount}`);
    }

    const trx = await db.transaction();
    try {
      // Get user wallet
      const wallet = await Wallet.findByUserId(userId);
      if (!wallet) {
        throw new Error('Wallet not found');
      }

      if (!wallet.is_active) {
        throw new Error('Wallet is not active');
      }

      // Check if user has sufficient balance
      if (await Wallet.hasInsufficientBalance(wallet.id, amount)) {
        throw new Error('Insufficient balance');
      }

      // Create transaction record
      const transactionId = HelperUtil.generateId();
      const reference = HelperUtil.generateReference();

      const transaction = await Transaction.createTransaction({
        id: transactionId,
        fromWalletId: wallet.id,
        amount,
        type: TxnType.DEBIT,
        status: TransactionStatus.PENDING,
        reference,
        description: description || 'Wallet withdrawal',
        metadata: {
          withdrawalType: 'manual',
          userId,
        },
      });

      // Update wallet balance
      const updatedWallet = await Wallet.decrementBalance(wallet.id, amount);

      // Update transaction status to completed
      await Transaction.updateStatus(transactionId, TransactionStatus.COMPLETED);

      await trx.commit();

      Logger.info('Withdrawal completed successfully', {
        userId,
        walletId: wallet.id,
        amount,
        reference,
        newBalance: updatedWallet.balance,
      });

      return {
        transaction: { ...transaction, status: TransactionStatus.COMPLETED },
        wallet: updatedWallet,
      };
    } catch (error) {
      await trx.rollback();
      Logger.error('Error withdrawing funds', {
        userId,
        amount,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  static async getWalletTransactions(userId: string, limit: number = 50): Promise<TransactionType[]> {
    const wallet = await Wallet.findByUserId(userId);
    if (!wallet) {
      throw new Error('Wallet not found');
    }

    return Transaction.findByWalletId(wallet.id, limit);
  }

  static async getWalletBalance(userId: string): Promise<number> {
    const wallet = await Wallet.findByUserId(userId);
    if (!wallet) {
      throw new Error('Wallet not found');
    }

    return parseFloat(wallet.balance.toString());
  }
}
