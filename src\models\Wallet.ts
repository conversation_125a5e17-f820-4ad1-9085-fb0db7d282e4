import { BaseModel } from './BaseModel';
import { Wallet as WalletType } from '@/types';

export class Wallet extends BaseModel {
  protected static tableName = 'wallets';

  static async findByUserId(userId: string): Promise<WalletType | null> {
    return this.query.where('user_id', userId).first();
  }

  static async createWallet(walletData: {
    id: string;
    userId: string;
    balance?: number;
    currency?: string;
  }): Promise<WalletType> {
    const wallet = {
      id: walletData.id,
      user_id: walletData.userId,
      balance: walletData.balance || 0.00,
      currency: walletData.currency || 'NGN',
      is_active: true,
    };

    return this.create(wallet);
  }

  static async updateBalance(id: string, newBalance: number): Promise<WalletType> {
    return this.update(id, { balance: newBalance });
  }

  static async incrementBalance(id: string, amount: number): Promise<WalletType> {
    const wallet = await this.findById(id);
    if (!wallet) {
      throw new Error('Wallet not found');
    }
    
    const newBalance = parseFloat(wallet.balance) + amount;
    return this.updateBalance(id, newBalance);
  }

  static async decrementBalance(id: string, amount: number): Promise<WalletType> {
    const wallet = await this.findById(id);
    if (!wallet) {
      throw new Error('Wallet not found');
    }
    
    const currentBalance = parseFloat(wallet.balance);
    if (currentBalance < amount) {
      throw new Error('Insufficient balance');
    }
    
    const newBalance = currentBalance - amount;
    return this.updateBalance(id, newBalance);
  }

  static async getBalance(id: string): Promise<number> {
    const wallet = await this.findById(id);
    if (!wallet) {
      throw new Error('Wallet not found');
    }
    
    return parseFloat(wallet.balance);
  }

  static async hasInsufficientBalance(id: string, amount: number): Promise<boolean> {
    const balance = await this.getBalance(id);
    return balance < amount;
  }

  static async deactivateWallet(id: string): Promise<WalletType> {
    return this.update(id, { is_active: false });
  }

  static async activateWallet(id: string): Promise<WalletType> {
    return this.update(id, { is_active: true });
  }

  static async getActiveWallets(): Promise<WalletType[]> {
    return this.query.where('is_active', true).select('*');
  }
}
