import { Request, Response } from 'express';
import { WalletService } from '@/services/WalletService';
import { ResponseUtil } from '@/utils/response';
import { Logger } from '@/utils/logger';
import { FundWalletRequest, TransferRequest, WithdrawRequest } from '@/types';

export class WalletController {
  static async getWallet(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      
      const wallet = await WalletService.getUserWallet(userId);
      
      ResponseUtil.success(res, 'Wallet retrieved successfully', {
        wallet,
      });
    } catch (error: any) {
      Logger.error('Get wallet error', {
        error: error.message,
        userId: req.user?.id,
      });
      
      if (error.message === 'Wallet not found for user') {
        ResponseUtil.notFound(res, error.message);
        return;
      }
      
      ResponseUtil.internalServerError(res, 'Failed to retrieve wallet');
    }
  }

  static async getBalance(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      
      const balance = await WalletService.getWalletBalance(userId);
      
      ResponseUtil.success(res, 'Balance retrieved successfully', {
        balance,
        formattedBalance: new Intl.NumberFormat('en-NG', {
          style: 'currency',
          currency: 'NGN',
        }).format(balance),
      });
    } catch (error: any) {
      Logger.error('Get balance error', {
        error: error.message,
        userId: req.user?.id,
      });
      
      if (error.message === 'Wallet not found') {
        ResponseUtil.notFound(res, error.message);
        return;
      }
      
      ResponseUtil.internalServerError(res, 'Failed to retrieve balance');
    }
  }

  static async fundWallet(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const fundData: FundWalletRequest = req.body;
      
      const result = await WalletService.fundWallet(userId, fundData);
      
      ResponseUtil.success(res, 'Wallet funded successfully', {
        transaction: result.transaction,
        wallet: result.wallet,
      });
    } catch (error: any) {
      Logger.error('Fund wallet error', {
        error: error.message,
        userId: req.user?.id,
        amount: req.body.amount,
      });
      
      ResponseUtil.badRequest(res, error.message);
    }
  }

  static async transferFunds(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const transferData: TransferRequest = req.body;
      
      const result = await WalletService.transferFunds(userId, transferData);
      
      ResponseUtil.success(res, 'Transfer completed successfully', {
        transaction: result.transaction,
        senderWallet: result.senderWallet,
        recipientWallet: {
          id: result.recipientWallet.id,
          balance: result.recipientWallet.balance,
        },
      });
    } catch (error: any) {
      Logger.error('Transfer funds error', {
        error: error.message,
        userId: req.user?.id,
        recipientEmail: req.body.recipientEmail,
        amount: req.body.amount,
      });
      
      ResponseUtil.badRequest(res, error.message);
    }
  }

  static async withdrawFunds(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const withdrawData: WithdrawRequest = req.body;
      
      const result = await WalletService.withdrawFunds(userId, withdrawData);
      
      ResponseUtil.success(res, 'Withdrawal completed successfully', {
        transaction: result.transaction,
        wallet: result.wallet,
      });
    } catch (error: any) {
      Logger.error('Withdraw funds error', {
        error: error.message,
        userId: req.user?.id,
        amount: req.body.amount,
      });
      
      ResponseUtil.badRequest(res, error.message);
    }
  }

  static async getTransactions(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const limit = parseInt(req.query.limit as string) || 50;
      
      if (limit > 100) {
        ResponseUtil.badRequest(res, 'Limit cannot exceed 100');
        return;
      }
      
      const transactions = await WalletService.getWalletTransactions(userId, limit);
      
      ResponseUtil.success(res, 'Transactions retrieved successfully', {
        transactions,
        count: transactions.length,
      });
    } catch (error: any) {
      Logger.error('Get transactions error', {
        error: error.message,
        userId: req.user?.id,
      });
      
      if (error.message === 'Wallet not found') {
        ResponseUtil.notFound(res, error.message);
        return;
      }
      
      ResponseUtil.internalServerError(res, 'Failed to retrieve transactions');
    }
  }
}
