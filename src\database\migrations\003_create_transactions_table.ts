import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('transactions', (table) => {
    table.string('id', 36).primary();
    table.string('from_wallet_id', 36).nullable();
    table.string('to_wallet_id', 36).nullable();
    table.decimal('amount', 15, 2).notNullable();
    table.enum('type', ['credit', 'debit', 'transfer']).notNullable();
    table.enum('status', ['pending', 'completed', 'failed', 'reversed']).defaultTo('pending');
    table.string('reference', 100).notNullable().unique();
    table.text('description').nullable();
    table.json('metadata').nullable();
    table.timestamps(true, true);
    
    // Foreign key constraints
    table.foreign('from_wallet_id').references('id').inTable('wallets').onDelete('SET NULL');
    table.foreign('to_wallet_id').references('id').inTable('wallets').onDelete('SET NULL');
    
    // Indexes
    table.index(['from_wallet_id']);
    table.index(['to_wallet_id']);
    table.index(['type']);
    table.index(['status']);
    table.index(['reference']);
    table.index(['created_at']);
    
    // Composite indexes for common queries
    table.index(['from_wallet_id', 'status']);
    table.index(['to_wallet_id', 'status']);
    table.index(['type', 'status']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('transactions');
}
