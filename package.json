{"name": "lendsqr-wallet-service", "version": "1.0.0", "description": "MVP wallet service for Demo Credit mobile lending app", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "migrate": "knex migrate:latest", "migrate:rollback": "knex migrate:rollback", "seed": "knex seed:run", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["wallet", "fintech", "nodejs", "typescript", "mysql", "knex"], "author": "Lendsqr Backend Engineer Candidate", "license": "MIT", "dependencies": {"express": "^4.18.2", "knex": "^3.0.1", "mysql2": "^3.6.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "axios": "^1.6.2", "uuid": "^9.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.4", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1"}, "engines": {"node": ">=18.0.0"}}