import { Request, Response, NextFunction } from 'express';
import { ResponseUtil } from '@/utils/response';
import { Logger } from '@/utils/logger';

export const errorHandler = (
  error: any,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  Logger.error('Unhandled error', {
    error: error.message,
    stack: error.stack,
    path: req.path,
    method: req.method,
    body: req.body,
    query: req.query,
    params: req.params,
  });

  // Handle specific error types
  if (error.name === 'ValidationError') {
    ResponseUtil.badRequest(res, 'Validation error', error.message);
    return;
  }

  if (error.name === 'UnauthorizedError') {
    ResponseUtil.unauthorized(res, error.message);
    return;
  }

  if (error.code === 'ER_DUP_ENTRY') {
    ResponseUtil.conflict(res, 'Duplicate entry', 'Resource already exists');
    return;
  }

  if (error.code === 'ER_NO_REFERENCED_ROW_2') {
    ResponseUtil.badRequest(res, 'Invalid reference', 'Referenced resource does not exist');
    return;
  }

  // Default to internal server error
  ResponseUtil.internalServerError(res, 'An unexpected error occurred');
};

export const notFoundHandler = (req: Request, res: Response): void => {
  ResponseUtil.notFound(res, `Route ${req.method} ${req.path} not found`);
};
