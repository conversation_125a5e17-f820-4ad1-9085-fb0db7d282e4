# Lendsqr Wallet Service

A robust MVP wallet service for Demo Credit mobile lending app built with Node.js, TypeScript, MySQL, and KnexJS ORM. This service provides core wallet functionality including user registration, wallet funding, transfers, and withdrawals with integration to Lendsqr Adjutor Karma blacklist API.

## 🏗️ Architecture Overview

This application follows a clean, modular architecture with clear separation of concerns:

- **Controllers**: Handle HTTP requests and responses
- **Services**: Contain business logic and orchestrate operations
- **Models**: Data access layer with database interactions
- **Middleware**: Authentication, validation, and error handling
- **Utils**: Helper functions and utilities
- **Database**: MySQL with KnexJS ORM for migrations and queries

## 📊 Entity-Relationship Diagram

```
┌─────────────────┐       ┌─────────────────┐       ┌─────────────────┐
│      USERS      │       │     WALLETS     │       │  TRANSACTIONS   │
├─────────────────┤       ├─────────────────┤       ├─────────────────┤
│ id (PK)         │◄─────┐│ id (PK)         │       │ id (PK)         │
│ email (UNIQUE)  │      └│ user_id (FK)    │◄─────┐│ from_wallet_id  │
│ first_name      │       │ balance         │      ││ to_wallet_id    │
│ last_name       │       │ currency        │      ││ amount          │
│ phone_number    │       │ is_active       │      ││ type            │
│ password_hash   │       │ created_at      │      ││ status          │
│ is_blacklisted  │       │ updated_at      │      ││ reference       │
│ is_active       │       └─────────────────┘      ││ description     │
│ created_at      │                                ││ metadata        │
│ updated_at      │                                ││ created_at      │
└─────────────────┘                                ││ updated_at      │
                                                   │└─────────────────┘
                                                   │
                                                   └─ WALLETS.id (FK)
```

### Relationships:
- **Users → Wallets**: One-to-One (Each user has exactly one wallet)
- **Wallets → Transactions**: One-to-Many (Each wallet can have multiple transactions)
- **Transactions**: Self-referencing through from_wallet_id and to_wallet_id

## 🚀 Features

### Core Functionality
- ✅ **User Registration**: Create account with Karma blacklist verification
- ✅ **User Authentication**: JWT-based authentication system
- ✅ **Wallet Management**: Automatic wallet creation for new users
- ✅ **Fund Wallet**: Add money to user wallet
- ✅ **Transfer Funds**: Send money to other users via email
- ✅ **Withdraw Funds**: Remove money from wallet
- ✅ **Transaction History**: View all wallet transactions
- ✅ **Balance Inquiry**: Check current wallet balance

### Security & Validation
- ✅ **Karma Blacklist Integration**: Prevents onboarding of blacklisted users
- ✅ **Input Validation**: Comprehensive request validation using Joi
- ✅ **Password Security**: Bcrypt hashing with configurable rounds
- ✅ **JWT Authentication**: Secure token-based authentication
- ✅ **SQL Injection Protection**: Parameterized queries via KnexJS
- ✅ **CORS Protection**: Configurable cross-origin resource sharing
- ✅ **Helmet Security**: Security headers and protection

### Database Features
- ✅ **ACID Transactions**: Ensures data consistency for financial operations
- ✅ **Database Migrations**: Version-controlled schema changes
- ✅ **Database Seeding**: Sample data for development and testing
- ✅ **Connection Pooling**: Optimized database connections
- ✅ **Indexes**: Optimized queries with proper indexing

## 🛠️ Technology Stack

- **Runtime**: Node.js (LTS)
- **Language**: TypeScript
- **Framework**: Express.js
- **Database**: MySQL
- **ORM**: KnexJS
- **Authentication**: JWT (jsonwebtoken)
- **Validation**: Joi
- **Testing**: Jest + Supertest
- **Security**: Helmet, CORS, Bcrypt
- **Logging**: Custom logger utility
- **Environment**: dotenv

## 📋 Prerequisites

- Node.js (v18.0.0 or higher)
- MySQL (v8.0 or higher)
- npm or yarn package manager

## ⚙️ Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd lendsqr-wallet-service
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Configuration
```bash
cp .env.example .env
```

Edit `.env` file with your configuration:
```env
# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=lendsqr_wallet

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=24h

# Lendsqr Adjutor Karma API
KARMA_API_URL=https://adjutor.lendsqr.com/v2/verification/karma
KARMA_API_KEY=your-karma-api-key-here

# Application Configuration
BCRYPT_ROUNDS=12
MAX_TRANSFER_AMOUNT=1000000
MIN_TRANSFER_AMOUNT=1
```

### 4. Database Setup
```bash
# Create database
mysql -u root -p -e "CREATE DATABASE lendsqr_wallet;"

# Run migrations
npm run migrate

# Seed database (optional)
npm run seed
```

### 5. Build and Start
```bash
# Development
npm run dev

# Production
npm run build
npm start
```

## 🧪 Testing

### Run Tests
```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

### Test Coverage
The application includes comprehensive unit tests covering:
- ✅ **Service Layer**: Business logic and data operations
- ✅ **Controller Layer**: HTTP request/response handling
- ✅ **Authentication**: JWT token validation and user verification
- ✅ **Validation**: Input validation and error scenarios
- ✅ **Database Operations**: CRUD operations and transactions
- ✅ **Error Handling**: Edge cases and error scenarios

## 📚 API Documentation

### Base URL
```
http://localhost:3000/api
```

### Authentication
Most endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

### Endpoints

#### Authentication Endpoints

##### Register User
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "phoneNumber": "+2348123456789",
  "password": "Password123!"
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "phone_number": "+2348123456789",
      "is_blacklisted": false,
      "is_active": true,
      "created_at": "2023-12-01T00:00:00.000Z",
      "updated_at": "2023-12-01T00:00:00.000Z"
    },
    "token": "jwt_token_here"
  }
}
```

##### Login User
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Password123!"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "phone_number": "+2348123456789",
      "is_blacklisted": false,
      "is_active": true,
      "created_at": "2023-12-01T00:00:00.000Z",
      "updated_at": "2023-12-01T00:00:00.000Z"
    },
    "token": "jwt_token_here"
  }
}
```

##### Get User Profile
```http
GET /api/auth/profile
Authorization: Bearer <jwt_token>
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Profile retrieved successfully",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "phone_number": "+2348123456789",
      "is_blacklisted": false,
      "is_active": true,
      "created_at": "2023-12-01T00:00:00.000Z",
      "updated_at": "2023-12-01T00:00:00.000Z"
    }
  }
}
```

#### Wallet Endpoints

##### Get Wallet Details
```http
GET /api/wallet
Authorization: Bearer <jwt_token>
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Wallet retrieved successfully",
  "data": {
    "wallet": {
      "id": "uuid",
      "user_id": "uuid",
      "balance": "1500.00",
      "currency": "NGN",
      "is_active": true,
      "created_at": "2023-12-01T00:00:00.000Z",
      "updated_at": "2023-12-01T00:00:00.000Z"
    }
  }
}
```

##### Get Wallet Balance
```http
GET /api/wallet/balance
Authorization: Bearer <jwt_token>
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Balance retrieved successfully",
  "data": {
    "balance": 1500.00,
    "formattedBalance": "₦1,500.00"
  }
}
```

##### Fund Wallet
```http
POST /api/wallet/fund
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "amount": 1000.00,
  "description": "Wallet funding"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Wallet funded successfully",
  "data": {
    "transaction": {
      "id": "uuid",
      "from_wallet_id": null,
      "to_wallet_id": "uuid",
      "amount": "1000.00",
      "type": "credit",
      "status": "completed",
      "reference": "TXN_1701388800000_ABC123",
      "description": "Wallet funding",
      "metadata": "{\"fundingType\":\"manual\",\"userId\":\"uuid\"}",
      "created_at": "2023-12-01T00:00:00.000Z",
      "updated_at": "2023-12-01T00:00:00.000Z"
    },
    "wallet": {
      "id": "uuid",
      "user_id": "uuid",
      "balance": "1000.00",
      "currency": "NGN",
      "is_active": true,
      "created_at": "2023-12-01T00:00:00.000Z",
      "updated_at": "2023-12-01T00:00:00.000Z"
    }
  }
}
```

##### Transfer Funds
```http
POST /api/wallet/transfer
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "recipientEmail": "<EMAIL>",
  "amount": 500.00,
  "description": "Payment for services"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Transfer completed successfully",
  "data": {
    "transaction": {
      "id": "uuid",
      "from_wallet_id": "sender_wallet_uuid",
      "to_wallet_id": "recipient_wallet_uuid",
      "amount": "500.00",
      "type": "transfer",
      "status": "completed",
      "reference": "TXN_1701388800000_XYZ789",
      "description": "Payment for services",
      "metadata": "{\"senderId\":\"uuid\",\"recipientId\":\"uuid\",\"recipientEmail\":\"<EMAIL>\"}",
      "created_at": "2023-12-01T00:00:00.000Z",
      "updated_at": "2023-12-01T00:00:00.000Z"
    },
    "senderWallet": {
      "id": "sender_wallet_uuid",
      "user_id": "sender_uuid",
      "balance": "500.00",
      "currency": "NGN",
      "is_active": true,
      "created_at": "2023-12-01T00:00:00.000Z",
      "updated_at": "2023-12-01T00:00:00.000Z"
    },
    "recipientWallet": {
      "id": "recipient_wallet_uuid",
      "balance": "500.00"
    }
  }
}
```

##### Withdraw Funds
```http
POST /api/wallet/withdraw
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "amount": 200.00,
  "description": "Cash withdrawal"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Withdrawal completed successfully",
  "data": {
    "transaction": {
      "id": "uuid",
      "from_wallet_id": "uuid",
      "to_wallet_id": null,
      "amount": "200.00",
      "type": "debit",
      "status": "completed",
      "reference": "TXN_1701388800000_DEF456",
      "description": "Cash withdrawal",
      "metadata": "{\"withdrawalType\":\"manual\",\"userId\":\"uuid\"}",
      "created_at": "2023-12-01T00:00:00.000Z",
      "updated_at": "2023-12-01T00:00:00.000Z"
    },
    "wallet": {
      "id": "uuid",
      "user_id": "uuid",
      "balance": "300.00",
      "currency": "NGN",
      "is_active": true,
      "created_at": "2023-12-01T00:00:00.000Z",
      "updated_at": "2023-12-01T00:00:00.000Z"
    }
  }
}
```

##### Get Transaction History
```http
GET /api/wallet/transactions?limit=20
Authorization: Bearer <jwt_token>
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Transactions retrieved successfully",
  "data": {
    "transactions": [
      {
        "id": "uuid",
        "from_wallet_id": "uuid",
        "to_wallet_id": null,
        "amount": "200.00",
        "type": "debit",
        "status": "completed",
        "reference": "TXN_1701388800000_DEF456",
        "description": "Cash withdrawal",
        "metadata": "{\"withdrawalType\":\"manual\",\"userId\":\"uuid\"}",
        "created_at": "2023-12-01T00:00:00.000Z",
        "updated_at": "2023-12-01T00:00:00.000Z"
      }
    ],
    "count": 1
  }
}
```

### Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error information (optional)"
}
```

**Common HTTP Status Codes:**
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors, business logic errors)
- `401` - Unauthorized (missing or invalid token)
- `403` - Forbidden (blacklisted user, inactive account)
- `404` - Not Found (resource doesn't exist)
- `409` - Conflict (duplicate resource)
- `500` - Internal Server Error

## 🏛️ Design Decisions & Architecture

### Database Design
1. **UUID Primary Keys**: Used for better security and distributed system compatibility
2. **Decimal for Money**: Used `DECIMAL(15,2)` for precise financial calculations
3. **Soft Deletes**: Implemented via `is_active` flags for data retention
4. **Audit Trail**: `created_at` and `updated_at` timestamps on all tables
5. **Indexes**: Strategic indexing on frequently queried columns
6. **Foreign Key Constraints**: Ensures referential integrity

### Security Considerations
1. **Password Hashing**: Bcrypt with configurable rounds
2. **JWT Tokens**: Stateless authentication with expiration
3. **Input Validation**: Comprehensive validation using Joi schemas
4. **SQL Injection Prevention**: Parameterized queries via KnexJS
5. **Blacklist Integration**: Real-time verification against Karma API
6. **Rate Limiting**: Can be implemented at reverse proxy level

### Transaction Handling
1. **ACID Compliance**: Database transactions for financial operations
2. **Idempotency**: Unique transaction references prevent duplicates
3. **Status Tracking**: Transaction status for audit and reconciliation
4. **Rollback Capability**: Automatic rollback on errors
5. **Metadata Storage**: JSON metadata for additional transaction context

### Code Quality Principles
1. **DRY (Don't Repeat Yourself)**: Shared utilities and base classes
2. **WET (Write Everything Twice)**: Acceptable duplication for clarity
3. **SOLID Principles**: Single responsibility, dependency injection
4. **Error Handling**: Consistent error responses and logging
5. **Type Safety**: Full TypeScript coverage with strict mode

## 📁 Project Structure

```
lendsqr-wallet-service/
├── src/
│   ├── __tests__/              # Test files
│   │   ├── controllers/        # Controller tests
│   │   ├── services/           # Service tests
│   │   ├── helpers/            # Test utilities
│   │   └── setup.ts            # Test setup configuration
│   ├── config/                 # Configuration files
│   │   ├── database.ts         # Database connection
│   │   └── index.ts            # App configuration
│   ├── controllers/            # HTTP request handlers
│   │   ├── AuthController.ts   # Authentication endpoints
│   │   └── WalletController.ts # Wallet endpoints
│   ├── database/               # Database related files
│   │   ├── migrations/         # Database migrations
│   │   └── seeds/              # Database seeds
│   ├── middleware/             # Express middleware
│   │   ├── auth.ts             # Authentication middleware
│   │   ├── validation.ts       # Request validation
│   │   └── errorHandler.ts     # Error handling
│   ├── models/                 # Data access layer
│   │   ├── BaseModel.ts        # Base model class
│   │   ├── User.ts             # User model
│   │   ├── Wallet.ts           # Wallet model
│   │   └── Transaction.ts      # Transaction model
│   ├── routes/                 # API routes
│   │   ├── auth.ts             # Authentication routes
│   │   ├── wallet.ts           # Wallet routes
│   │   └── index.ts            # Route aggregator
│   ├── services/               # Business logic layer
│   │   ├── AuthService.ts      # Authentication service
│   │   ├── WalletService.ts    # Wallet service
│   │   └── KarmaService.ts     # Karma API integration
│   ├── types/                  # TypeScript type definitions
│   │   └── index.ts            # Type definitions
│   ├── utils/                  # Utility functions
│   │   ├── helpers.ts          # General helpers
│   │   ├── logger.ts           # Logging utility
│   │   ├── response.ts         # Response formatting
│   │   └── validation.ts       # Validation schemas
│   ├── app.ts                  # Express app configuration
│   └── index.ts                # Application entry point
├── .env.example                # Environment variables template
├── .gitignore                  # Git ignore rules
├── .eslintrc.js               # ESLint configuration
├── jest.config.js             # Jest test configuration
├── knexfile.ts                # Knex database configuration
├── package.json               # Dependencies and scripts
├── tsconfig.json              # TypeScript configuration
└── README.md                  # Project documentation
```

## 🚀 Deployment

### Environment Variables
Ensure all required environment variables are set in production:

```bash
# Required
DB_HOST=your-db-host
DB_USER=your-db-user
DB_PASSWORD=your-db-password
DB_NAME=your-db-name
JWT_SECRET=your-secure-jwt-secret
KARMA_API_KEY=your-karma-api-key

# Optional
PORT=3000
NODE_ENV=production
BCRYPT_ROUNDS=12
```

### Production Checklist
- [ ] Set `NODE_ENV=production`
- [ ] Use strong JWT secret (minimum 32 characters)
- [ ] Configure proper CORS origins
- [ ] Set up database connection pooling
- [ ] Configure logging and monitoring
- [ ] Set up SSL/TLS certificates
- [ ] Configure reverse proxy (Nginx/Apache)
- [ ] Set up database backups
- [ ] Configure rate limiting
- [ ] Set up health checks

## 🔧 Development

### Code Style
- ESLint configuration for consistent code style
- Prettier for code formatting
- TypeScript strict mode enabled
- Conventional commit messages

### Git Workflow
```bash
# Feature development
git checkout -b feature/new-feature
git commit -m "feat: add new feature"
git push origin feature/new-feature

# Create pull request for review
```

### Database Migrations
```bash
# Create new migration
npx knex migrate:make migration_name

# Run migrations
npm run migrate

# Rollback last migration
npm run migrate:rollback
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **Lendsqr Team** for the assessment opportunity
- **Node.js Community** for excellent tooling and libraries
- **TypeScript Team** for type safety and developer experience
- **KnexJS Team** for the excellent query builder and migration system

## 📞 Support

For questions or support regarding this assessment project, please contact the development team.

---

**Note**: This is an assessment project for Lendsqr backend engineering position. The code is for evaluation purposes and should not be used in production without proper security review and additional hardening.
```