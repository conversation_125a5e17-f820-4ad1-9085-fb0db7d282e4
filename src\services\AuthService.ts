import { User } from '@/models/User';
import { Wallet } from '@/models/Wallet';
import { KarmaService } from './KarmaService';
import { HelperUtil } from '@/utils/helpers';
import { CreateUserRequest, LoginRequest, User as UserType } from '@/types';
import { Logger } from '@/utils/logger';
import db from '@/config/database';

export class AuthService {
  static async registerUser(userData: CreateUserRequest): Promise<{
    user: UserType;
    token: string;
  }> {
    const { email, firstName, lastName, phoneNumber, password } = userData;

    // Sanitize inputs
    const sanitizedEmail = HelperUtil.sanitizeEmail(email);
    const sanitizedPhoneNumber = HelperUtil.sanitizePhoneNumber(phoneNumber);

    // Check if user already exists
    const existingUserByEmail = await User.findByEmail(sanitizedEmail);
    if (existingUserByEmail) {
      throw new Error('User with this email already exists');
    }

    const existingUserByPhone = await User.findByPhoneNumber(sanitizedPhoneNumber);
    if (existingUserByPhone) {
      throw new Error('User with this phone number already exists');
    }

    // Check Karma blacklist
    const isBlacklisted = await KarmaService.isUserBlacklisted(sanitizedEmail, sanitizedPhoneNumber);
    if (isBlacklisted) {
      Logger.warn('Attempted registration by blacklisted user', {
        email: sanitizedEmail,
        phoneNumber: sanitizedPhoneNumber,
      });
      throw new Error('User cannot be onboarded due to blacklist status');
    }

    // Hash password
    const passwordHash = await HelperUtil.hashPassword(password);

    // Create user and wallet in a transaction
    const trx = await db.transaction();
    try {
      const userId = HelperUtil.generateId();
      
      // Create user
      const user = await User.createUser({
        id: userId,
        email: sanitizedEmail,
        firstName,
        lastName,
        phoneNumber: sanitizedPhoneNumber,
        passwordHash,
        isBlacklisted,
      });

      // Create wallet for the user
      const walletId = HelperUtil.generateId();
      await Wallet.createWallet({
        id: walletId,
        userId,
      });

      await trx.commit();

      // Generate JWT token
      const token = HelperUtil.generateToken({
        userId: user.id,
        email: user.email,
      });

      Logger.info('User registered successfully', {
        userId: user.id,
        email: user.email,
      });

      return { user, token };
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  static async loginUser(loginData: LoginRequest): Promise<{
    user: UserType;
    token: string;
  }> {
    const { email, password } = loginData;
    const sanitizedEmail = HelperUtil.sanitizeEmail(email);

    // Find user by email
    const user = await User.findByEmail(sanitizedEmail);
    if (!user) {
      throw new Error('Invalid email or password');
    }

    // Check if user is active
    if (!user.is_active) {
      throw new Error('Account is deactivated');
    }

    // Check if user is blacklisted
    if (user.is_blacklisted) {
      throw new Error('Account is restricted');
    }

    // Verify password
    const isPasswordValid = await HelperUtil.comparePassword(password, user.password_hash);
    if (!isPasswordValid) {
      throw new Error('Invalid email or password');
    }

    // Generate JWT token
    const token = HelperUtil.generateToken({
      userId: user.id,
      email: user.email,
    });

    Logger.info('User logged in successfully', {
      userId: user.id,
      email: user.email,
    });

    return { user, token };
  }

  static async getUserById(userId: string): Promise<UserType | null> {
    return User.findById(userId);
  }

  static async updateUserBlacklistStatus(userId: string, isBlacklisted: boolean): Promise<UserType> {
    const user = await User.updateBlacklistStatus(userId, isBlacklisted);
    
    Logger.info('User blacklist status updated', {
      userId,
      isBlacklisted,
    });

    return user;
  }
}
